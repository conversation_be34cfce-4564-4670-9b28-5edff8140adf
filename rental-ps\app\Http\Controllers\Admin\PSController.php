<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PS;
use Illuminate\Http\Request;

class PSController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $psUnits = PS::all();
        return view('admin.ps.index', compact('psUnits'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.ps.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_ps' => 'required|string|max:255',
            'tipe' => 'required|in:PS4,PS5',
            'harga_per_jam' => 'required|numeric|min:0',
        ]);

        PS::create($request->all());

        return redirect()->route('admin.ps.index')
            ->with('success', 'PS Unit berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PS $ps)
    {
        return view('admin.ps.show', compact('ps'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PS $ps)
    {
        return view('admin.ps.edit', compact('ps'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PS $ps)
    {
        $request->validate([
            'nama_ps' => 'required|string|max:255',
            'tipe' => 'required|in:PS4,PS5',
            'status' => 'required|in:tersedia,disewa',
            'harga_per_jam' => 'required|numeric|min:0',
        ]);

        $ps->update($request->all());

        return redirect()->route('admin.ps.index')
            ->with('success', 'PS Unit berhasil diupdate.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PS $ps)
    {
        $ps->delete();

        return redirect()->route('admin.ps.index')
            ->with('success', 'PS Unit berhasil dihapus.');
    }
}
