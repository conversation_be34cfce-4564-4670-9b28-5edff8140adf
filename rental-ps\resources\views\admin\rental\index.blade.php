@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('Rental Management') }}</span>
                    <a href="{{ route('admin.rental.create') }}" class="btn btn-primary">Add New Rental</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User</th>
                                    <th>PS Unit</th>
                                    <th>Game</th>
                                    <th>Waktu <PERSON></th>
                                    <th>Waktu <PERSON></th>
                                    <th>Status</th>
                                    <th>Total Harga</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($rentals as $rental)
                                <tr>
                                    <td>{{ $rental->id }}</td>
                                    <td>{{ $rental->user->name }}</td>
                                    <td>{{ $rental->ps->nama_ps }}</td>
                                    <td>{{ $rental->game->nama_game }}</td>
                                    <td>{{ $rental->waktu_mulai->format('d/m/Y H:i') }}</td>
                                    <td>{{ $rental->waktu_selesai->format('d/m/Y H:i') }}</td>
                                    <td>
                                        <span class="badge badge-{{ $rental->status == 'pending' ? 'warning' : ($rental->status == 'disetujui' ? 'success' : 'secondary') }}">
                                            {{ ucfirst($rental->status) }}
                                        </span>
                                    </td>
                                    <td>Rp {{ number_format($rental->total_harga, 0, ',', '.') }}</td>
                                    <td>
                                        <a href="{{ route('admin.rental.show', $rental) }}" class="btn btn-info btn-sm">View</a>
                                        <a href="{{ route('admin.rental.edit', $rental) }}" class="btn btn-warning btn-sm">Edit</a>
                                        <form action="{{ route('admin.rental.destroy', $rental) }}" method="POST" style="display: inline-block;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="9" class="text-center">No rentals found.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
