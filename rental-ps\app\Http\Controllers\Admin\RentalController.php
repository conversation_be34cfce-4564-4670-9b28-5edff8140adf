<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Rental;
use App\Models\PS;
use App\Models\Game;
use App\Models\User;
use Illuminate\Http\Request;

class RentalController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $rentals = Rental::with(['user', 'ps', 'game'])->latest()->get();
        return view('admin.rental.index', compact('rentals'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $users = User::where('role', 'user')->get();
        $psUnits = PS::where('status', 'tersedia')->get();
        $games = Game::all();

        return view('admin.rental.create', compact('users', 'psUnits', 'games'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'ps_id' => 'required|exists:ps_units,id',
            'game_id' => 'required|exists:games,id',
            'waktu_mulai' => 'required|date',
            'waktu_selesai' => 'required|date|after:waktu_mulai',
            'total_harga' => 'required|numeric|min:0',
        ]);

        Rental::create($request->all());

        // Update status PS menjadi disewa
        PS::find($request->ps_id)->update(['status' => 'disewa']);

        return redirect()->route('admin.rental.index')
            ->with('success', 'Rental berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Rental $rental)
    {
        $rental->load(['user', 'ps', 'game']);
        return view('admin.rental.show', compact('rental'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Rental $rental)
    {
        $users = User::where('role', 'user')->get();
        $psUnits = PS::all();
        $games = Game::all();

        return view('admin.rental.edit', compact('rental', 'users', 'psUnits', 'games'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Rental $rental)
    {
        $request->validate([
            'status' => 'required|in:pending,disetujui,selesai',
        ]);

        $oldStatus = $rental->status;
        $rental->update($request->only('status'));

        // Jika status berubah menjadi selesai, update status PS menjadi tersedia
        if ($request->status === 'selesai' && $oldStatus !== 'selesai') {
            $rental->ps->update(['status' => 'tersedia']);
        }

        return redirect()->route('admin.rental.index')
            ->with('success', 'Status rental berhasil diupdate.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Rental $rental)
    {
        // Jika rental dihapus, kembalikan status PS menjadi tersedia
        if ($rental->status !== 'selesai') {
            $rental->ps->update(['status' => 'tersedia']);
        }

        $rental->delete();

        return redirect()->route('admin.rental.index')
            ->with('success', 'Rental berhasil dihapus.');
    }
}
