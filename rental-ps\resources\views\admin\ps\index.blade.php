@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('PS Units Management') }}</span>
                    <a href="{{ route('admin.ps.create') }}" class="btn btn-primary">Add New PS Unit</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nama PS</th>
                                    <th>Tipe</th>
                                    <th>Status</th>
                                    <th>Harga per Jam</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($psUnits as $ps)
                                <tr>
                                    <td>{{ $ps->id }}</td>
                                    <td>{{ $ps->nama_ps }}</td>
                                    <td>{{ $ps->tipe }}</td>
                                    <td>
                                        <span class="badge badge-{{ $ps->status == 'tersedia' ? 'success' : 'warning' }}">
                                            {{ ucfirst($ps->status) }}
                                        </span>
                                    </td>
                                    <td>Rp {{ number_format($ps->harga_per_jam, 0, ',', '.') }}</td>
                                    <td>
                                        <a href="{{ route('admin.ps.show', $ps) }}" class="btn btn-info btn-sm">View</a>
                                        <a href="{{ route('admin.ps.edit', $ps) }}" class="btn btn-warning btn-sm">Edit</a>
                                        <form action="{{ route('admin.ps.destroy', $ps) }}" method="POST" style="display: inline-block;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="6" class="text-center">No PS units found.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
