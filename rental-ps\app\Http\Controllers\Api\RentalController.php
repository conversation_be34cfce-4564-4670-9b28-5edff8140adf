<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Rental;
use App\Models\PS;
use App\Models\Game;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RentalController extends Controller
{
    public function index(Request $request)
    {
        $rentals = Rental::with(['ps', 'game'])
            ->where('user_id', $request->user()->id)
            ->latest()
            ->get();

        return response()->json([
            'success' => true,
            'data' => $rentals
        ]);
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'ps_id' => 'required|exists:ps_units,id',
            'game_id' => 'required|exists:games,id',
            'waktu_mulai' => 'required|date|after:now',
            'waktu_selesai' => 'required|date|after:waktu_mulai',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        // Check if PS is available
        $ps = PS::find($request->ps_id);
        if ($ps->status !== 'tersedia') {
            return response()->json([
                'success' => false,
                'message' => 'PS unit is not available'
            ], 400);
        }

        // Calculate total price (example: based on hours)
        $waktuMulai = new \DateTime($request->waktu_mulai);
        $waktuSelesai = new \DateTime($request->waktu_selesai);
        $hours = $waktuSelesai->diff($waktuMulai)->h + ($waktuSelesai->diff($waktuMulai)->days * 24);
        $totalHarga = $hours * $ps->harga_per_jam;

        $rental = Rental::create([
            'user_id' => $request->user()->id,
            'ps_id' => $request->ps_id,
            'game_id' => $request->game_id,
            'waktu_mulai' => $request->waktu_mulai,
            'waktu_selesai' => $request->waktu_selesai,
            'total_harga' => $totalHarga,
            'status' => 'pending',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Rental request created successfully',
            'data' => $rental->load(['ps', 'game'])
        ], 201);
    }

    public function show(Request $request, $id)
    {
        $rental = Rental::with(['ps', 'game'])
            ->where('user_id', $request->user()->id)
            ->findOrFail($id);

        return response()->json([
            'success' => true,
            'data' => $rental
        ]);
    }

    public function getAvailablePS()
    {
        $psUnits = PS::where('status', 'tersedia')->get();

        return response()->json([
            'success' => true,
            'data' => $psUnits
        ]);
    }

    public function getGames()
    {
        $games = Game::all();

        return response()->json([
            'success' => true,
            'data' => $games
        ]);
    }
}
