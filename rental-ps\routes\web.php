<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\PSController;
use App\Http\Controllers\Admin\GameController;
use App\Http\Controllers\Admin\RentalController;
use App\Http\Controllers\Admin\UserController;

// Redirect root to admin login
Route::get('/', function () {
    return redirect()->route('login');
});

// Authentication Routes
Auth::routes();

// Admin Routes (protected by auth and admin middleware)
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // User Management
    Route::resource('users', UserController::class);
    Route::patch('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // PS Management
    Route::resource('ps', PSController::class);

    // Game Management
    Route::resource('games', GameController::class);

    // Rental Management
    Route::resource('rental', RentalController::class);
});

Auth::routes();

Route::get('/home', [App\Http\Controllers\HomeController::class, 'index'])->name('home');
