<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\PSController;
use App\Http\Controllers\Admin\GameController;
use App\Http\Controllers\Admin\RentalController;
use App\Http\Controllers\Admin\UserController;

// Landing Page - Halaman Utama
Route::get('/', function () {
    return view('welcome');
})->name('home');

// Authentication Routes
Auth::routes();

// Admin Routes (protected by auth and admin middleware)
Route::middleware(['auth', 'admin'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // User Management
    Route::resource('users', UserController::class);
    Route::patch('users/{user}/toggle-status', [UserController::class, 'toggleStatus'])->name('users.toggle-status');

    // PS Management
    Route::resource('ps', PSController::class);

    // Game Management
    Route::resource('games', GameController::class);

    // Rental Management
    Route::resource('rental', RentalController::class);
});

// Redirect after login based on role
Route::get('/home', function () {
    if (auth()->check()) {
        if (auth()->user()->role === 'admin') {
            return redirect()->route('admin.dashboard');
        }
        // For regular users, you can redirect to a user dashboard or profile
        return redirect()->route('home');
    }
    return redirect()->route('login');
})->name('redirect.home');
