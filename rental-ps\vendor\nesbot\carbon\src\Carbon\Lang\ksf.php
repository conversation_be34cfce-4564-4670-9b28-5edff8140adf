<?php

/**
 * This file is part of the Carbon package.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */

return array_replace_recursive(require __DIR__.'/en.php', [
    'meridiem' => ['sárúwá', 'cɛɛ́nko'],
    'weekdays' => ['sɔ́ndǝ', 'lǝndí', 'maadí', 'mɛkrɛdí', 'jǝǝdí', 'júmbá', 'samdí'],
    'weekdays_short' => ['sɔ́n', 'lǝn', 'maa', 'mɛk', 'jǝǝ', 'júm', 'sam'],
    'weekdays_min' => ['sɔ́n', 'lǝn', 'maa', 'mɛk', 'jǝǝ', 'júm', 'sam'],
    'months' => ['ŋwí<PERSON> a ntɔ́ntɔ', 'ŋwíí akǝ bɛ́ɛ', 'ŋw<PERSON><PERSON> akǝ ráá', 'ŋw<PERSON><PERSON> akǝ nin', 'ŋw<PERSON><PERSON> akǝ táan', 'ŋ<PERSON><PERSON><PERSON> akǝ táafɔk', 'ŋw<PERSON><PERSON> akǝ táabɛɛ', 'ŋw<PERSON><PERSON> akǝ táaraa', 'ŋw<PERSON><PERSON> akǝ táanin', 'ŋw<PERSON><PERSON> akǝ ntɛk', 'ŋwíí akǝ ntɛk di bɔ́k', 'ŋwíí akǝ ntɛk di bɛ́ɛ'],
    'months_short' => ['ŋ1', 'ŋ2', 'ŋ3', 'ŋ4', 'ŋ5', 'ŋ6', 'ŋ7', 'ŋ8', 'ŋ9', 'ŋ10', 'ŋ11', 'ŋ12'],
    'first_day_of_week' => 1,
    'formats' => [
        'LT' => 'HH:mm',
        'LTS' => 'HH:mm:ss',
        'L' => 'D/M/YYYY',
        'LL' => 'D MMM YYYY',
        'LLL' => 'D MMMM YYYY HH:mm',
        'LLLL' => 'dddd D MMMM YYYY HH:mm',
    ],
]);
