@extends('layouts.admin')

@section('title', 'Detail User')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-md-4">
            <!-- User Profile Card -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-user me-2"></i>Profil User
                    </h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.users.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left me-1"></i>Kembali
                        </a>
                    </div>
                </div>
                <div class="card-body text-center">
                    <div class="avatar-lg mx-auto mb-3">
                        <div class="avatar-title bg-primary rounded-circle">
                            {{ strtoupper(substr($user->name, 0, 2)) }}
                        </div>
                    </div>
                    <h4 class="mb-1">{{ $user->name }}</h4>
                    <p class="text-muted mb-3">{{ $user->email }}</p>
                    
                    <div class="row text-center">
                        <div class="col-6">
                            <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : 'info' }} fs-6">
                                {{ ucfirst($user->role) }}
                            </span>
                        </div>
                        <div class="col-6">
                            <span class="badge bg-{{ ($user->is_active ?? true) ? 'success' : 'secondary' }} fs-6">
                                {{ ($user->is_active ?? true) ? 'Aktif' : 'Nonaktif' }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>Edit User
                        </a>
                        @if($user->id !== auth()->id())
                            <form action="{{ route('admin.users.toggle-status', $user) }}" method="POST">
                                @csrf
                                @method('PATCH')
                                <button type="submit" class="btn btn-{{ ($user->is_active ?? true) ? 'secondary' : 'success' }} w-100">
                                    <i class="fas fa-{{ ($user->is_active ?? true) ? 'ban' : 'check' }} me-1"></i>
                                    {{ ($user->is_active ?? true) ? 'Nonaktifkan' : 'Aktifkan' }}
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <!-- User Information -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-info-circle me-2"></i>Informasi Detail
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0"><strong>ID User</strong></p>
                        </div>
                        <div class="col-sm-9">
                            <p class="text-muted mb-0">#{{ $user->id }}</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0"><strong>Nama Lengkap</strong></p>
                        </div>
                        <div class="col-sm-9">
                            <p class="text-muted mb-0">{{ $user->name }}</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0"><strong>Email</strong></p>
                        </div>
                        <div class="col-sm-9">
                            <p class="text-muted mb-0">{{ $user->email }}</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0"><strong>Role</strong></p>
                        </div>
                        <div class="col-sm-9">
                            <span class="badge bg-{{ $user->role === 'admin' ? 'danger' : 'info' }}">
                                {{ ucfirst($user->role) }}
                            </span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0"><strong>Status</strong></p>
                        </div>
                        <div class="col-sm-9">
                            <span class="badge bg-{{ ($user->is_active ?? true) ? 'success' : 'secondary' }}">
                                {{ ($user->is_active ?? true) ? 'Aktif' : 'Nonaktif' }}
                            </span>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0"><strong>Terdaftar</strong></p>
                        </div>
                        <div class="col-sm-9">
                            <p class="text-muted mb-0">{{ $user->created_at->format('d F Y, H:i') }} WIB</p>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-sm-3">
                            <p class="mb-0"><strong>Terakhir Update</strong></p>
                        </div>
                        <div class="col-sm-9">
                            <p class="text-muted mb-0">{{ $user->updated_at->format('d F Y, H:i') }} WIB</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Activity/Statistics -->
            <div class="card mt-3">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar me-2"></i>Statistik Aktivitas
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-4">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $user->rentals()->count() ?? 0 }}</h3>
                                            <p class="mb-0">Total Rental</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-gamepad fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $user->rentals()->where('status', 'selesai')->count() ?? 0 }}</h3>
                                            <p class="mb-0">Rental Selesai</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-check-circle fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex align-items-center">
                                        <div class="flex-grow-1">
                                            <h3 class="mb-0">{{ $user->rentals()->whereIn('status', ['pending', 'disetujui'])->count() ?? 0 }}</h3>
                                            <p class="mb-0">Rental Aktif</p>
                                        </div>
                                        <div class="flex-shrink-0">
                                            <i class="fas fa-clock fa-2x"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
}

.avatar-title {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 600;
}
</style>
@endsection
