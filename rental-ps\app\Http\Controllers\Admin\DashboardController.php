<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PS;
use App\Models\Game;
use App\Models\Rental;
use App\Models\User;
use Illuminate\Http\Request;

class DashboardController extends Controller
{
    public function index()
    {
        $totalPS = PS::count();
        $totalGames = Game::count();
        $totalUsers = User::where('role', 'user')->count();
        $totalRentals = Rental::count();
        $activeRentals = Rental::where('status', 'disetujui')->count();
        $pendingRentals = Rental::where('status', 'pending')->count();

        $recentRentals = Rental::with(['user', 'ps', 'game'])
            ->latest()
            ->take(5)
            ->get();

        return view('admin.dashboard', compact(
            'totalPS',
            'totalGames',
            'totalUsers',
            'totalRentals',
            'activeRentals',
            'pendingRentals',
            'recentRentals'
        ));
    }
}
