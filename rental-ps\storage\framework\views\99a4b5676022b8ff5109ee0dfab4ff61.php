<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header"><?php echo e(__('Admin Dashboard')); ?></div>

                <div class="card-body">
                    <?php if(session('status')): ?>
                        <div class="alert alert-success" role="alert">
                            <?php echo e(session('status')); ?>

                        </div>
                    <?php endif; ?>

                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-white bg-primary mb-3">
                                <div class="card-header">Total PS Units</div>
                                <div class="card-body">
                                    <h4 class="card-title"><?php echo e($totalPS); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-success mb-3">
                                <div class="card-header">Total Games</div>
                                <div class="card-body">
                                    <h4 class="card-title"><?php echo e($totalGames); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-info mb-3">
                                <div class="card-header">Total Users</div>
                                <div class="card-body">
                                    <h4 class="card-title"><?php echo e($totalUsers); ?></h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-warning mb-3">
                                <div class="card-header">Total Rentals</div>
                                <div class="card-body">
                                    <h4 class="card-title"><?php echo e($totalRentals); ?></h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Quick Actions</div>
                                <div class="card-body">
                                    <a href="<?php echo e(route('admin.ps.index')); ?>" class="btn btn-primary mb-2">Manage PS Units</a><br>
                                    <a href="<?php echo e(route('admin.games.index')); ?>" class="btn btn-success mb-2">Manage Games</a><br>
                                    <a href="<?php echo e(route('admin.rental.index')); ?>" class="btn btn-info mb-2">Manage Rentals</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Rental Status</div>
                                <div class="card-body">
                                    <p><strong>Active Rentals:</strong> <?php echo e($activeRentals); ?></p>
                                    <p><strong>Pending Rentals:</strong> <?php echo e($pendingRentals); ?></p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Recent Rentals</div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>User</th>
                                                    <th>PS Unit</th>
                                                    <th>Game</th>
                                                    <th>Status</th>
                                                    <th>Total</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $recentRentals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rental): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <tr>
                                                    <td><?php echo e($rental->user->name); ?></td>
                                                    <td><?php echo e($rental->ps->nama_ps); ?></td>
                                                    <td><?php echo e($rental->game->nama_game); ?></td>
                                                    <td>
                                                        <span class="badge badge-<?php echo e($rental->status == 'pending' ? 'warning' : ($rental->status == 'disetujui' ? 'success' : 'secondary')); ?>">
                                                            <?php echo e(ucfirst($rental->status)); ?>

                                                        </span>
                                                    </td>
                                                    <td>Rp <?php echo e(number_format($rental->total_harga, 0, ',', '.')); ?></td>
                                                    <td><?php echo e($rental->created_at->format('d/m/Y')); ?></td>
                                                </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\RENTALPS\rental-ps\resources\views/admin/dashboard.blade.php ENDPATH**/ ?>