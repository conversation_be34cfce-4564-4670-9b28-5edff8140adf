<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Rental extends Model
{
    protected $fillable = [
        'user_id',
        'ps_id',
        'game_id',
        'waktu_mulai',
        'waktu_selesai',
        'status',
        'total_harga',
    ];

    protected $casts = [
        'waktu_mulai' => 'datetime',
        'waktu_selesai' => 'datetime',
    ];

    /**
     * Get the user that owns the rental.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the PS unit that is rented.
     */
    public function ps()
    {
        return $this->belongsTo(PS::class, 'ps_id');
    }

    /**
     * Get the game that is rented.
     */
    public function game()
    {
        return $this->belongsTo(Game::class);
    }
}
