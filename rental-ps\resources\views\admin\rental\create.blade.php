@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Add New Rental') }}</div>

                <div class="card-body">
                    <form method="POST" action="{{ route('admin.rental.store') }}">
                        @csrf

                        <div class="form-group row">
                            <label for="user_id" class="col-md-4 col-form-label text-md-right">{{ __('User') }}</label>

                            <div class="col-md-6">
                                <select id="user_id" class="form-control @error('user_id') is-invalid @enderror" name="user_id" required>
                                    <option value="">Select User</option>
                                    @foreach($users as $user)
                                        <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>{{ $user->name }} ({{ $user->email }})</option>
                                    @endforeach
                                </select>

                                @error('user_id')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="ps_id" class="col-md-4 col-form-label text-md-right">{{ __('PS Unit') }}</label>

                            <div class="col-md-6">
                                <select id="ps_id" class="form-control @error('ps_id') is-invalid @enderror" name="ps_id" required>
                                    <option value="">Select PS Unit</option>
                                    @foreach($psUnits as $ps)
                                        <option value="{{ $ps->id }}" {{ old('ps_id') == $ps->id ? 'selected' : '' }}>{{ $ps->nama_ps }} ({{ $ps->tipe }}) - Rp {{ number_format($ps->harga_per_jam, 0, ',', '.') }}/jam</option>
                                    @endforeach
                                </select>

                                @error('ps_id')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="game_id" class="col-md-4 col-form-label text-md-right">{{ __('Game') }}</label>

                            <div class="col-md-6">
                                <select id="game_id" class="form-control @error('game_id') is-invalid @enderror" name="game_id" required>
                                    <option value="">Select Game</option>
                                    @foreach($games as $game)
                                        <option value="{{ $game->id }}" {{ old('game_id') == $game->id ? 'selected' : '' }}>{{ $game->nama_game }} ({{ $game->platform }})</option>
                                    @endforeach
                                </select>

                                @error('game_id')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="waktu_mulai" class="col-md-4 col-form-label text-md-right">{{ __('Waktu Mulai') }}</label>

                            <div class="col-md-6">
                                <input id="waktu_mulai" type="datetime-local" class="form-control @error('waktu_mulai') is-invalid @enderror" name="waktu_mulai" value="{{ old('waktu_mulai') }}" required>

                                @error('waktu_mulai')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="waktu_selesai" class="col-md-4 col-form-label text-md-right">{{ __('Waktu Selesai') }}</label>

                            <div class="col-md-6">
                                <input id="waktu_selesai" type="datetime-local" class="form-control @error('waktu_selesai') is-invalid @enderror" name="waktu_selesai" value="{{ old('waktu_selesai') }}" required>

                                @error('waktu_selesai')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="total_harga" class="col-md-4 col-form-label text-md-right">{{ __('Total Harga') }}</label>

                            <div class="col-md-6">
                                <input id="total_harga" type="number" class="form-control @error('total_harga') is-invalid @enderror" name="total_harga" value="{{ old('total_harga') }}" required min="0" step="0.01">

                                @error('total_harga')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Create Rental') }}
                                </button>
                                <a href="{{ route('admin.rental.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
