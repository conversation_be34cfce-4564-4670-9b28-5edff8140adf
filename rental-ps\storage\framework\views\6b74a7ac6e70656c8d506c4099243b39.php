<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span><?php echo e(__('Rental Management')); ?></span>
                    <a href="<?php echo e(route('admin.rental.create')); ?>" class="btn btn-primary">Add New Rental</a>
                </div>

                <div class="card-body">
                    <?php if(session('success')): ?>
                        <div class="alert alert-success" role="alert">
                            <?php echo e(session('success')); ?>

                        </div>
                    <?php endif; ?>

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>User</th>
                                    <th>PS Unit</th>
                                    <th>Game</th>
                                    <th><PERSON><PERSON><PERSON></th>
                                    <th><PERSON><PERSON><PERSON></th>
                                    <th>Status</th>
                                    <th>Total Harga</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $rentals; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $rental): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <tr>
                                    <td><?php echo e($rental->id); ?></td>
                                    <td><?php echo e($rental->user->name); ?></td>
                                    <td><?php echo e($rental->ps->nama_ps); ?></td>
                                    <td><?php echo e($rental->game->nama_game); ?></td>
                                    <td><?php echo e($rental->waktu_mulai->format('d/m/Y H:i')); ?></td>
                                    <td><?php echo e($rental->waktu_selesai->format('d/m/Y H:i')); ?></td>
                                    <td>
                                        <span class="badge badge-<?php echo e($rental->status == 'pending' ? 'warning' : ($rental->status == 'disetujui' ? 'success' : 'secondary')); ?>">
                                            <?php echo e(ucfirst($rental->status)); ?>

                                        </span>
                                    </td>
                                    <td>Rp <?php echo e(number_format($rental->total_harga, 0, ',', '.')); ?></td>
                                    <td>
                                        <a href="<?php echo e(route('admin.rental.show', $rental)); ?>" class="btn btn-info btn-sm">View</a>
                                        <a href="<?php echo e(route('admin.rental.edit', $rental)); ?>" class="btn btn-warning btn-sm">Edit</a>
                                        <form action="<?php echo e(route('admin.rental.destroy', $rental)); ?>" method="POST" style="display: inline-block;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('DELETE'); ?>
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <tr>
                                    <td colspan="9" class="text-center">No rentals found.</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\RENTALPS\rental-ps\resources\views/admin/rental/index.blade.php ENDPATH**/ ?>