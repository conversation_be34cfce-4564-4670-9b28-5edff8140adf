@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Add New Game') }}</div>

                <div class="card-body">
                    <form method="POST" action="{{ route('admin.games.store') }}">
                        @csrf

                        <div class="form-group row">
                            <label for="nama_game" class="col-md-4 col-form-label text-md-right">{{ __('Nama Game') }}</label>

                            <div class="col-md-6">
                                <input id="nama_game" type="text" class="form-control @error('nama_game') is-invalid @enderror" name="nama_game" value="{{ old('nama_game') }}" required autocomplete="nama_game" autofocus>

                                @error('nama_game')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="jenis" class="col-md-4 col-form-label text-md-right">{{ __('Jenis') }}</label>

                            <div class="col-md-6">
                                <select id="jenis" class="form-control @error('jenis') is-invalid @enderror" name="jenis" required>
                                    <option value="">Select Type</option>
                                    <option value="CD" {{ old('jenis') == 'CD' ? 'selected' : '' }}>CD</option>
                                    <option value="Digital" {{ old('jenis') == 'Digital' ? 'selected' : '' }}>Digital</option>
                                </select>

                                @error('jenis')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="platform" class="col-md-4 col-form-label text-md-right">{{ __('Platform') }}</label>

                            <div class="col-md-6">
                                <input id="platform" type="text" class="form-control @error('platform') is-invalid @enderror" name="platform" value="{{ old('platform') }}" required autocomplete="platform">

                                @error('platform')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Create Game') }}
                                </button>
                                <a href="{{ route('admin.games.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
