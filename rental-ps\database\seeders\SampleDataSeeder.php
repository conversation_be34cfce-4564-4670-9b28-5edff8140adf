<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PS;
use App\Models\Game;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample PS units
        PS::create([
            'nama_ps' => 'PS4 Pro #1',
            'tipe' => 'PS4',
            'status' => 'tersedia',
            'harga_per_jam' => 15000,
        ]);

        PS::create([
            'nama_ps' => 'PS5 #1',
            'tipe' => 'PS5',
            'status' => 'tersedia',
            'harga_per_jam' => 25000,
        ]);

        PS::create([
            'nama_ps' => 'PS4 Slim #1',
            'tipe' => 'PS4',
            'status' => 'tersedia',
            'harga_per_jam' => 12000,
        ]);

        // Create sample games
        Game::create([
            'nama_game' => 'FIFA 24',
            'jenis' => 'CD',
            'platform' => 'PS4/PS5',
        ]);

        Game::create([
            'nama_game' => 'God of War',
            'jenis' => 'Digital',
            'platform' => 'PS4/PS5',
        ]);

        Game::create([
            'nama_game' => 'Spider-Man',
            'jenis' => 'CD',
            'platform' => 'PS4/PS5',
        ]);

        Game::create([
            'nama_game' => 'Call of Duty',
            'jenis' => 'Digital',
            'platform' => 'PS4/PS5',
        ]);
    }
}
