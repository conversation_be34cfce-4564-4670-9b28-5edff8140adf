@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Edit Rental Status') }}</div>

                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <h5>Rental Details:</h5>
                            <p><strong>User:</strong> {{ $rental->user->name }}</p>
                            <p><strong>PS Unit:</strong> {{ $rental->ps->nama_ps }} ({{ $rental->ps->tipe }})</p>
                            <p><strong>Game:</strong> {{ $rental->game->nama_game }}</p>
                            <p><strong>Waktu <PERSON>:</strong> {{ $rental->waktu_mulai->format('d/m/Y H:i') }}</p>
                            <p><strong>Waktu <PERSON>:</strong> {{ $rental->waktu_selesai->format('d/m/Y H:i') }}</p>
                            <p><strong>Total Harga:</strong> Rp {{ number_format($rental->total_harga, 0, ',', '.') }}</p>
                        </div>
                    </div>

                    <form method="POST" action="{{ route('admin.rental.update', $rental) }}">
                        @csrf
                        @method('PUT')

                        <div class="form-group row">
                            <label for="status" class="col-md-4 col-form-label text-md-right">{{ __('Status') }}</label>

                            <div class="col-md-6">
                                <select id="status" class="form-control @error('status') is-invalid @enderror" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="pending" {{ old('status', $rental->status) == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="disetujui" {{ old('status', $rental->status) == 'disetujui' ? 'selected' : '' }}>Disetujui</option>
                                    <option value="selesai" {{ old('status', $rental->status) == 'selesai' ? 'selected' : '' }}>Selesai</option>
                                </select>

                                @error('status')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Update Status') }}
                                </button>
                                <a href="{{ route('admin.rental.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
