@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">{{ __('Admin Dashboard') }}</div>

                <div class="card-body">
                    @if (session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif

                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-white bg-primary mb-3">
                                <div class="card-header">Total PS Units</div>
                                <div class="card-body">
                                    <h4 class="card-title">{{ $totalPS }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-success mb-3">
                                <div class="card-header">Total Games</div>
                                <div class="card-body">
                                    <h4 class="card-title">{{ $totalGames }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-info mb-3">
                                <div class="card-header">Total Users</div>
                                <div class="card-body">
                                    <h4 class="card-title">{{ $totalUsers }}</h4>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-white bg-warning mb-3">
                                <div class="card-header">Total Rentals</div>
                                <div class="card-body">
                                    <h4 class="card-title">{{ $totalRentals }}</h4>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Quick Actions</div>
                                <div class="card-body">
                                    <a href="{{ route('admin.ps.index') }}" class="btn btn-primary mb-2">Manage PS Units</a><br>
                                    <a href="{{ route('admin.games.index') }}" class="btn btn-success mb-2">Manage Games</a><br>
                                    <a href="{{ route('admin.rental.index') }}" class="btn btn-info mb-2">Manage Rentals</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">Rental Status</div>
                                <div class="card-body">
                                    <p><strong>Active Rentals:</strong> {{ $activeRentals }}</p>
                                    <p><strong>Pending Rentals:</strong> {{ $pendingRentals }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">Recent Rentals</div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>User</th>
                                                    <th>PS Unit</th>
                                                    <th>Game</th>
                                                    <th>Status</th>
                                                    <th>Total</th>
                                                    <th>Date</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($recentRentals as $rental)
                                                <tr>
                                                    <td>{{ $rental->user->name }}</td>
                                                    <td>{{ $rental->ps->nama_ps }}</td>
                                                    <td>{{ $rental->game->nama_game }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ $rental->status == 'pending' ? 'warning' : ($rental->status == 'disetujui' ? 'success' : 'secondary') }}">
                                                            {{ ucfirst($rental->status) }}
                                                        </span>
                                                    </td>
                                                    <td>Rp {{ number_format($rental->total_harga, 0, ',', '.') }}</td>
                                                    <td>{{ $rental->created_at->format('d/m/Y') }}</td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
