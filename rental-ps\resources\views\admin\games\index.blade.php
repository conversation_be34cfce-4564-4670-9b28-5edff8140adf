@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <span>{{ __('Games Management') }}</span>
                    <a href="{{ route('admin.games.create') }}" class="btn btn-primary">Add New Game</a>
                </div>

                <div class="card-body">
                    @if (session('success'))
                        <div class="alert alert-success" role="alert">
                            {{ session('success') }}
                        </div>
                    @endif

                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Nama Game</th>
                                    <th>Jenis</th>
                                    <th>Platform</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($games as $game)
                                <tr>
                                    <td>{{ $game->id }}</td>
                                    <td>{{ $game->nama_game }}</td>
                                    <td>
                                        <span class="badge badge-{{ $game->jenis == 'CD' ? 'primary' : 'success' }}">
                                            {{ $game->jenis }}
                                        </span>
                                    </td>
                                    <td>{{ $game->platform }}</td>
                                    <td>
                                        <a href="{{ route('admin.games.show', $game) }}" class="btn btn-info btn-sm">View</a>
                                        <a href="{{ route('admin.games.edit', $game) }}" class="btn btn-warning btn-sm">Edit</a>
                                        <form action="{{ route('admin.games.destroy', $game) }}" method="POST" style="display: inline-block;">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-danger btn-sm" onclick="return confirm('Are you sure?')">Delete</button>
                                        </form>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center">No games found.</td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
