<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Game;
use Illuminate\Http\Request;

class GameController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $games = Game::all();
        return view('admin.games.index', compact('games'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.games.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'nama_game' => 'required|string|max:255',
            'jenis' => 'required|in:CD,Digital',
            'platform' => 'required|string|max:255',
        ]);

        Game::create($request->all());

        return redirect()->route('admin.games.index')
            ->with('success', 'Game berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Game $game)
    {
        return view('admin.games.show', compact('game'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Game $game)
    {
        return view('admin.games.edit', compact('game'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Game $game)
    {
        $request->validate([
            'nama_game' => 'required|string|max:255',
            'jenis' => 'required|in:CD,Digital',
            'platform' => 'required|string|max:255',
        ]);

        $game->update($request->all());

        return redirect()->route('admin.games.index')
            ->with('success', 'Game berhasil diupdate.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Game $game)
    {
        $game->delete();

        return redirect()->route('admin.games.index')
            ->with('success', 'Game berhasil dihapus.');
    }
}
