@extends('layouts.app')

@section('content')
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">{{ __('Add New PS Unit') }}</div>

                <div class="card-body">
                    <form method="POST" action="{{ route('admin.ps.store') }}">
                        @csrf

                        <div class="form-group row">
                            <label for="nama_ps" class="col-md-4 col-form-label text-md-right">{{ __('Nama PS') }}</label>

                            <div class="col-md-6">
                                <input id="nama_ps" type="text" class="form-control @error('nama_ps') is-invalid @enderror" name="nama_ps" value="{{ old('nama_ps') }}" required autocomplete="nama_ps" autofocus>

                                @error('nama_ps')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="tipe" class="col-md-4 col-form-label text-md-right">{{ __('Tipe') }}</label>

                            <div class="col-md-6">
                                <select id="tipe" class="form-control @error('tipe') is-invalid @enderror" name="tipe" required>
                                    <option value="">Select Type</option>
                                    <option value="PS4" {{ old('tipe') == 'PS4' ? 'selected' : '' }}>PS4</option>
                                    <option value="PS5" {{ old('tipe') == 'PS5' ? 'selected' : '' }}>PS5</option>
                                </select>

                                @error('tipe')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row">
                            <label for="harga_per_jam" class="col-md-4 col-form-label text-md-right">{{ __('Harga per Jam') }}</label>

                            <div class="col-md-6">
                                <input id="harga_per_jam" type="number" class="form-control @error('harga_per_jam') is-invalid @enderror" name="harga_per_jam" value="{{ old('harga_per_jam') }}" required min="0" step="0.01">

                                @error('harga_per_jam')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                        </div>

                        <div class="form-group row mb-0">
                            <div class="col-md-6 offset-md-4">
                                <button type="submit" class="btn btn-primary">
                                    {{ __('Create PS Unit') }}
                                </button>
                                <a href="{{ route('admin.ps.index') }}" class="btn btn-secondary">Cancel</a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
